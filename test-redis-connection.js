const Redis = require('ioredis');

async function testConnection() {
  try {
    const redis = new Redis({
      host: 'localhost',
      port: 6379,
      password: 'pixelpark_secure_password'
    });
    
    console.log('Connecting to Redis...');
    
    redis.on('connect', () => {
      console.log('Connected successfully to <PERSON><PERSON>');
    });
    
    redis.on('error', (err) => {
      console.error('Redis connection error:', err);
    });
    
    // Test setting and getting a value
    await redis.set('test-key', 'test-value');
    const value = await redis.get('test-key');
    console.log('Test value retrieved:', value);
    
    // Close the connection
    redis.quit();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error in Redis test:', error);
  }
}

testConnection();
const { MongoClient } = require('mongodb');

async function testConnection() {
  try {
    const uri = 'mongodb://localhost:27017/pixel2';
    const client = new MongoClient(uri);
    
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully to MongoDB');
    
    const db = client.db();
    const stats = await db.stats();
    console.log('Database stats:', stats);
    
    await client.close();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
  }
}

testConnection();